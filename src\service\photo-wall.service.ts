import { Provide } from '@midwayjs/core';
import { PhotoWall } from '../entity/photo-wall.entity';
import { BaseService } from '../common/BaseService';
import { Order } from '../entity/order.entity';
import { ServicePhoto } from '../entity/service-photo.entity';
import { CustomError } from '../error/custom.error';

@Provide()
export class PhotoWallService extends BaseService<PhotoWall> {
  constructor() {
    super('照片墙');
  }

  getModel() {
    return PhotoWall;
  }

  /**
   * 根据订单ID查找照片墙记录
   */
  async findByOrder(orderId: number) {
    return await this.findOne({
      where: { orderId },
      include: [Order],
    });
  }

  /**
   * 获取最新照片列表
   */
  async findLatest(limit = 10) {
    return await this.findAll({
      query: { isEnabled: true },
      limit,
      order: [['priority', 'DESC'], ['createdAt', 'DESC']],
      include: [Order],
    });
  }

  /**
   * 获取热门照片列表（按点赞数排序）
   */
  async findPopular(limit = 10) {
    return await this.findAll({
      query: { isEnabled: true },
      limit,
      order: [['likeCount', 'DESC'], ['viewCount', 'DESC'], ['createdAt', 'DESC']],
      include: [Order],
    });
  }

  /**
   * 从服务照片创建照片墙记录
   */
  async createFromServicePhoto(orderId: number, data: {
    title?: string;
    description?: string;
    beforePhoto: string;
    afterPhoto: string;
    priority?: number;
  }) {
    // 检查是否已存在该订单的照片墙记录
    const existing = await this.findByOrder(orderId);
    if (existing) {
      throw new CustomError('该订单已存在照片墙记录');
    }

    // 获取订单信息
    const order = await Order.findByPk(orderId, {
      include: ['customer', 'orderDetails', 'orderDetails.pet', 'orderDetails.service', 'orderDetails.service.serviceType']
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 构建照片墙数据
    const photoWallData = {
      orderId,
      employeeId: order.employeeId,
      customerId: order.customerId,
      beforePhoto: data.beforePhoto,
      afterPhoto: data.afterPhoto,
      title: data.title || `${order.orderDetails?.[0]?.petName || '宠物'}的${order.orderDetails?.[0]?.service?.serviceType?.name || '服务'}`,
      description: data.description || '',
      serviceTypeName: order.orderDetails?.[0]?.service?.serviceType?.name || '',
      petName: order.orderDetails?.[0]?.petName || '',
      petType: order.orderDetails?.[0]?.petType || '',
      priority: data.priority || 0,
      isEnabled: true,
      likeCount: 0,
      viewCount: 0,
    };

    return await this.create(photoWallData);
  }

  /**
   * 增加浏览数
   */
  async incrementViewCount(id: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({
      viewCount: photoWall.viewCount + 1,
    });

    return photoWall;
  }

  /**
   * 增加点赞数
   */
  async incrementLikeCount(id: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({
      likeCount: photoWall.likeCount + 1,
    });

    return photoWall;
  }

  /**
   * 减少点赞数
   */
  async decrementLikeCount(id: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({
      likeCount: Math.max(0, photoWall.likeCount - 1),
    });

    return photoWall;
  }

  /**
   * 启用/禁用照片展示
   */
  async toggleEnabled(id: number, isEnabled: boolean) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({ isEnabled });
    return photoWall;
  }

  /**
   * 设置展示优先级
   */
  async setPriority(id: number, priority: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({ priority });
    return photoWall;
  }

  /**
   * 获取照片墙统计信息
   */
  async getStatistics() {
    const total = await PhotoWall.count();
    const enabled = await PhotoWall.count({ where: { isEnabled: true } });
    const totalViews = await PhotoWall.sum('viewCount');
    const totalLikes = await PhotoWall.sum('likeCount');

    return {
      total,
      enabled,
      disabled: total - enabled,
      totalViews: totalViews || 0,
      totalLikes: totalLikes || 0,
    };
  }
}
