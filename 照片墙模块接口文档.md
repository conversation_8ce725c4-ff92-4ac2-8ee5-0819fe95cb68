# 照片墙模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 照片墙数据结构
```typescript
interface PhotoWall {
  id: number;                    // 照片ID
  orderId?: number;              // 关联订单ID
  employeeId?: number;           // 关联员工ID
  customerId?: number;           // 关联客户ID
  beforePhoto: string;           // 服务前照片链接
  afterPhoto: string;            // 服务后照片链接
  title?: string;                // 照片标题
  description?: string;          // 照片描述
  serviceTypeName?: string;      // 服务类型名称
  petName?: string;              // 宠物名称
  petType?: string;              // 宠物类型
  isEnabled: boolean;            // 是否启用展示
  priority: number;              // 展示优先级
  likeCount: number;             // 点赞数
  viewCount: number;             // 浏览数
  createdAt?: Date;              // 创建时间
  updatedAt?: Date;              // 更新时间
}
```

---

## 1. 用户端接口

### 1.1 获取最新照片列表
**接口地址：** `GET /photo-walls/latest/list`  
**接口描述：** 获取最新的照片墙展示列表  
**是否需要认证：** 否  
**适用端：** 用户端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | number | 否 | 返回数量限制，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "beforePhoto": "https://example.com/before1.jpg",
        "afterPhoto": "https://example.com/after1.jpg",
        "title": "小白的洗护服务",
        "description": "专业洗护，焕然一新",
        "serviceTypeName": "洗护服务",
        "petName": "小白",
        "petType": "狗",
        "likeCount": 15,
        "viewCount": 120,
        "createdAt": "2024-01-01T10:00:00.000Z",
        "order": {
          "id": 1,
          "sn": "ORD20240101001"
        }
      }
    ],
    "total": 50
  }
}
```

### 1.2 获取热门照片列表
**接口地址：** `GET /photo-walls/popular/list`  
**接口描述：** 获取热门照片列表（按点赞数排序）  
**是否需要认证：** 否  
**适用端：** 用户端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | number | 否 | 返回数量限制，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 2,
        "beforePhoto": "https://example.com/before2.jpg",
        "afterPhoto": "https://example.com/after2.jpg",
        "title": "小黑的美容服务",
        "description": "造型设计，萌萌哒",
        "serviceTypeName": "美容服务",
        "petName": "小黑",
        "petType": "猫",
        "likeCount": 25,
        "viewCount": 200,
        "createdAt": "2024-01-02T10:00:00.000Z"
      }
    ],
    "total": 50
  }
}
```

### 1.3 查看照片详情
**接口地址：** `GET /photo-walls/{id}`  
**接口描述：** 查看照片详情，自动增加浏览数  
**是否需要认证：** 否  
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "customerId": 1,
    "beforePhoto": "https://example.com/before1.jpg",
    "afterPhoto": "https://example.com/after1.jpg",
    "title": "小白的洗护服务",
    "description": "专业洗护，焕然一新",
    "serviceTypeName": "洗护服务",
    "petName": "小白",
    "petType": "狗",
    "isEnabled": true,
    "priority": 10,
    "likeCount": 15,
    "viewCount": 121,
    "createdAt": "2024-01-01T10:00:00.000Z",
    "updatedAt": "2024-01-01T15:30:00.000Z"
  }
}
```

### 1.4 点赞照片
**接口地址：** `POST /photo-walls/{id}/like`  
**接口描述：** 为照片点赞  
**是否需要认证：** 否  
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "likeCount": 16
  }
}
```

### 1.5 取消点赞
**接口地址：** `POST /photo-walls/{id}/unlike`  
**接口描述：** 取消照片点赞  
**是否需要认证：** 否  
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "likeCount": 15
  }
}
```

### 1.6 查询订单相关照片
**接口地址：** `GET /photo-walls/order/{orderId}`
**接口描述：** 查询指定订单的照片墙记录
**是否需要认证：** 是
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "beforePhoto": "https://example.com/before1.jpg",
    "afterPhoto": "https://example.com/after1.jpg",
    "title": "小白的洗护服务",
    "description": "专业洗护，焕然一新",
    "likeCount": 15,
    "viewCount": 120,
    "order": {
      "id": 1,
      "sn": "ORD20240101001",
      "status": "已完成"
    }
  }
}
```

---

## 2. 员工端接口

### 2.1 从服务照片创建照片墙记录
**接口地址：** `POST /photo-walls/create-from-service`
**接口描述：** 员工从服务照片创建照片墙展示记录
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "title": "小白的洗护服务",
  "description": "专业洗护，焕然一新",
  "beforePhoto": "https://example.com/before1.jpg",
  "afterPhoto": "https://example.com/after1.jpg",
  "priority": 10
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| beforePhoto | string | 是 | 服务前照片URL |
| afterPhoto | string | 是 | 服务后照片URL |
| title | string | 否 | 照片标题，不填则自动生成 |
| description | string | 否 | 照片描述 |
| priority | number | 否 | 展示优先级，默认0 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "customerId": 1,
    "beforePhoto": "https://example.com/before1.jpg",
    "afterPhoto": "https://example.com/after1.jpg",
    "title": "小白的洗护服务",
    "description": "专业洗护，焕然一新",
    "serviceTypeName": "洗护服务",
    "petName": "小白",
    "petType": "狗",
    "isEnabled": true,
    "priority": 10,
    "likeCount": 0,
    "viewCount": 0,
    "createdAt": "2024-01-01T10:00:00.000Z"
  }
}
```

### 2.2 获取可用于照片墙的服务照片列表
**接口地址：** `GET /photo-walls/available-service-photos`
**接口描述：** 获取可用于创建照片墙的服务照片列表，支持筛选和排除已使用的照片
**是否需要认证：** 是
**适用端：** 员工端、管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| employeeId | number | 否 | 员工ID筛选 |
| customerId | number | 否 | 客户ID筛选 |
| serviceTypeName | string | 否 | 服务类型名称筛选 |
| hasBeforePhoto | boolean | 否 | 是否有服务前照片 |
| hasAfterPhoto | boolean | 否 | 是否有服务后照片 |
| excludeUsed | boolean | 否 | 是否排除已用于照片墙的照片，默认true |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "employeeId": 1,
        "beforePhotos": [
          "https://example.com/before1.jpg",
          "https://example.com/before2.jpg"
        ],
        "afterPhotos": [
          "https://example.com/after1.jpg",
          "https://example.com/after2.jpg"
        ],
        "beforePhotoTime": "2024-01-01T14:00:00.000Z",
        "afterPhotoTime": "2024-01-01T16:00:00.000Z",
        "order": {
          "id": 1,
          "sn": "ORD20240101001",
          "status": "已完成",
          "serviceTime": "2024-01-01T14:00:00.000Z",
          "customer": {
            "id": 1,
            "nickname": "张三",
            "phone": "13800138000"
          },
          "orderDetails": [
            {
              "id": 1,
              "serviceName": "宠物洗护",
              "petName": "小白",
              "petType": "狗",
              "service": {
                "id": 1,
                "serviceName": "宠物洗护",
                "serviceType": {
                  "id": 1,
                  "name": "洗护服务"
                }
              }
            }
          ]
        }
      }
    ],
    "total": 15
  }
}
```

### 2.3 从选择的服务照片创建照片墙记录
**接口地址：** `POST /photo-walls/create-from-selected`
**接口描述：** 从可选的服务照片中选择特定照片创建照片墙记录
**是否需要认证：** 是
**适用端：** 员工端、管理端

**请求体：**
```json
{
  "servicePhotoId": 1,
  "selectedBeforePhoto": "https://example.com/before2.jpg",
  "selectedAfterPhoto": "https://example.com/after1.jpg",
  "title": "小白的精美洗护",
  "description": "选择最佳效果照片展示",
  "priority": 20
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| servicePhotoId | number | 是 | 服务照片记录ID |
| selectedBeforePhoto | string | 是 | 选择的服务前照片URL |
| selectedAfterPhoto | string | 是 | 选择的服务后照片URL |
| title | string | 否 | 照片标题，不填则自动生成 |
| description | string | 否 | 照片描述 |
| priority | number | 否 | 展示优先级，默认0 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 2,
    "orderId": 1,
    "employeeId": 1,
    "customerId": 1,
    "beforePhoto": "https://example.com/before2.jpg",
    "afterPhoto": "https://example.com/after1.jpg",
    "title": "小白的精美洗护",
    "description": "选择最佳效果照片展示",
    "serviceTypeName": "洗护服务",
    "petName": "小白",
    "petType": "狗",
    "isEnabled": true,
    "priority": 20,
    "likeCount": 0,
    "viewCount": 0,
    "createdAt": "2024-01-01T18:00:00.000Z"
  }
}
```

---

## 3. 管理端接口

### 3.1 查询照片墙列表
**接口地址：** `GET /photo-walls`
**接口描述：** 管理端查询照片墙列表，支持分页和筛选
**是否需要认证：** 是
**适用端：** 管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |
| isEnabled | boolean | 否 | 是否启用筛选 |
| employeeId | number | 否 | 员工ID筛选 |
| customerId | number | 否 | 客户ID筛选 |
| serviceTypeName | string | 否 | 服务类型名称筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "employeeId": 1,
        "customerId": 1,
        "beforePhoto": "https://example.com/before1.jpg",
        "afterPhoto": "https://example.com/after1.jpg",
        "title": "小白的洗护服务",
        "description": "专业洗护，焕然一新",
        "serviceTypeName": "洗护服务",
        "petName": "小白",
        "petType": "狗",
        "isEnabled": true,
        "priority": 10,
        "likeCount": 15,
        "viewCount": 120,
        "createdAt": "2024-01-01T10:00:00.000Z",
        "order": {
          "id": 1,
          "sn": "ORD20240101001",
          "customer": {
            "id": 1,
            "nickname": "张三"
          }
        }
      }
    ],
    "total": 100
  }
}
```

### 3.2 新增照片墙记录
**接口地址：** `POST /photo-walls`
**接口描述：** 管理端手动新增照片墙记录
**是否需要认证：** 是
**适用端：** 管理端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "customerId": 1,
  "beforePhoto": "https://example.com/before1.jpg",
  "afterPhoto": "https://example.com/after1.jpg",
  "title": "小白的洗护服务",
  "description": "专业洗护，焕然一新",
  "serviceTypeName": "洗护服务",
  "petName": "小白",
  "petType": "狗",
  "priority": 10
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "customerId": 1,
    "beforePhoto": "https://example.com/before1.jpg",
    "afterPhoto": "https://example.com/after1.jpg",
    "title": "小白的洗护服务",
    "isEnabled": true,
    "priority": 10,
    "likeCount": 0,
    "viewCount": 0
  }
}
```

### 3.3 更新照片墙记录
**接口地址：** `PUT /photo-walls/{id}`
**接口描述：** 管理端更新照片墙记录信息
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**请求体：**
```json
{
  "title": "更新后的标题",
  "description": "更新后的描述",
  "priority": 20,
  "isEnabled": true
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 3.4 删除照片墙记录
**接口地址：** `DELETE /photo-walls/{id}`
**接口描述：** 管理端删除照片墙记录
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 3.5 启用/禁用照片展示
**接口地址：** `PUT /photo-walls/{id}/toggle-enabled`
**接口描述：** 管理端启用或禁用照片展示
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**请求体：**
```json
{
  "isEnabled": false
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| isEnabled | boolean | 是 | 是否启用展示 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "isEnabled": false
  }
}
```

### 3.6 设置展示优先级
**接口地址：** `PUT /photo-walls/{id}/priority`
**接口描述：** 管理端设置照片展示优先级
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

**请求体：**
```json
{
  "priority": 100
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| priority | number | 是 | 展示优先级，数值越大优先级越高 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "priority": 100
  }
}
```

### 3.7 获取照片墙统计信息
**接口地址：** `GET /photo-walls/statistics`
**接口描述：** 管理端获取照片墙统计信息
**是否需要认证：** 是
**适用端：** 管理端

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 150,
    "enabled": 120,
    "disabled": 30,
    "totalViews": 5000,
    "totalLikes": 800
  }
}
```

### 3.8 获取可用服务照片素材
**接口地址：** `GET /photo-walls/available-service-photos`
**接口描述：** 管理端获取可用于照片墙的服务照片素材列表
**是否需要认证：** 是
**适用端：** 管理端

**查询参数：** 同员工端接口 2.2

**响应示例：** 同员工端接口 2.2

### 3.9 从选择的服务照片创建照片墙
**接口地址：** `POST /photo-walls/create-from-selected`
**接口描述：** 管理端从选择的服务照片创建照片墙记录
**是否需要认证：** 是
**适用端：** 管理端

**请求参数：** 同员工端接口 2.3

**响应示例：** 同员工端接口 2.3

## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录已过期 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 5. 注意事项

1. **照片展示逻辑**：只有 `isEnabled: true` 的照片才会在用户端展示
2. **优先级排序**：照片按优先级（priority）降序、创建时间降序排列
3. **自动数据填充**：从服务照片创建时会自动填充订单相关信息
4. **浏览数统计**：每次查看照片详情会自动增加浏览数
5. **点赞机制**：支持点赞和取消点赞，点赞数不会低于0
6. **权限控制**：用户端只能查看和点赞，员工端可创建，管理端拥有全部权限
7. **照片要求**：服务前后照片都必须提供，用于展示对比效果
8. **数据关联**：照片墙记录与订单、员工、客户关联，便于数据追溯
9. **素材选择功能**：
   - 支持从现有服务照片中选择最佳照片创建照片墙
   - 可筛选特定员工、客户、服务类型的照片
   - 默认排除已用于照片墙的照片，避免重复
   - 支持从多张服务前后照片中选择最满意的组合
10. **照片验证**：选择的照片必须存在于对应的服务照片记录中
11. **唯一性约束**：每个订单只能创建一条照片墙记录
12. **筛选功能**：
    - 可按是否有服务前/后照片筛选
    - 可按服务类型、员工、客户筛选
    - 支持排除已使用的照片
