import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { PhotoWallService } from '../service/photo-wall.service';
import { CustomError } from '../error/custom.error';

@Controller('/photo-walls')
export class PhotoWallController {
  @Inject()
  ctx: Context;

  @Inject()
  service: PhotoWallService;

  @Get('/', { summary: '查询照片墙列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: ['order'],
      order: [['priority', 'DESC'], ['createdAt', 'DESC']],
    });
  }

  @Get('/:id', { summary: '按ID查询照片' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定照片');
    }

    // 增加浏览数
    await this.service.incrementViewCount(Number(id));

    return res;
  }

  @Post('/', { summary: '新增照片' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新照片信息' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除照片' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/order/:orderId', { summary: '查询订单相关照片' })
  async findByOrder(@Param('orderId') orderId: number) {
    return await this.service.findByOrder(orderId);
  }

  @Get('/latest/list', { summary: '获取最新照片列表' })
  async findLatest(@Query('limit') limit: number) {
    return await this.service.findLatest(limit || 10);
  }

  @Get('/popular/list', { summary: '获取热门照片列表' })
  async findPopular(@Query('limit') limit: number) {
    return await this.service.findPopular(limit || 10);
  }

  @Post('/create-from-service', { summary: '从服务照片创建照片墙记录' })
  async createFromServicePhoto(@Body() body: {
    orderId: number;
    title?: string;
    description?: string;
    beforePhoto: string;
    afterPhoto: string;
    priority?: number;
  }) {
    if (!body.orderId || !body.beforePhoto || !body.afterPhoto) {
      throw new CustomError('订单ID、服务前照片和服务后照片不能为空', 400);
    }

    return await this.service.createFromServicePhoto(body.orderId, {
      title: body.title,
      description: body.description,
      beforePhoto: body.beforePhoto,
      afterPhoto: body.afterPhoto,
      priority: body.priority,
    });
  }

  @Post('/:id/like', { summary: '点赞照片' })
  async like(@Param('id') id: number) {
    return await this.service.incrementLikeCount(id);
  }

  @Post('/:id/unlike', { summary: '取消点赞' })
  async unlike(@Param('id') id: number) {
    return await this.service.decrementLikeCount(id);
  }

  @Put('/:id/toggle-enabled', { summary: '启用/禁用照片展示' })
  async toggleEnabled(@Param('id') id: number, @Body() { isEnabled }: { isEnabled: boolean }) {
    return await this.service.toggleEnabled(id, isEnabled);
  }

  @Put('/:id/priority', { summary: '设置展示优先级' })
  async setPriority(@Param('id') id: number, @Body() { priority }: { priority: number }) {
    return await this.service.setPriority(id, priority);
  }

  @Get('/statistics', { summary: '获取照片墙统计信息' })
  async getStatistics() {
    return await this.service.getStatistics();
  }

  @Get('/available-service-photos', { summary: '获取可用于照片墙的服务照片列表' })
  async getAvailableServicePhotos(@Query() query: {
    current?: number;
    pageSize?: number;
    employeeId?: number;
    customerId?: number;
    serviceTypeName?: string;
    hasBeforePhoto?: boolean;
    hasAfterPhoto?: boolean;
    excludeUsed?: boolean;
  }) {
    return await this.service.getAvailableServicePhotos(query);
  }

  @Post('/create-from-selected', { summary: '从选择的服务照片创建照片墙记录' })
  async createFromSelectedPhotos(@Body() body: {
    servicePhotoId: number;
    selectedBeforePhoto: string;
    selectedAfterPhoto: string;
    title?: string;
    description?: string;
    priority?: number;
  }) {
    if (!body.servicePhotoId || !body.selectedBeforePhoto || !body.selectedAfterPhoto) {
      throw new CustomError('服务照片ID、选择的服务前照片和服务后照片不能为空', 400);
    }

    return await this.service.createFromSelectedPhotos({
      servicePhotoId: body.servicePhotoId,
      selectedBeforePhoto: body.selectedBeforePhoto,
      selectedAfterPhoto: body.selectedAfterPhoto,
      title: body.title,
      description: body.description,
      priority: body.priority,
    });
  }
}
